<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Preview App - Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-file-excel text-success me-2"></i>
                    Excel Preview App - Test Page
                </h1>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">API Health Check</h5>
                    </div>
                    <div class="card-body">
                        <button id="healthCheck" class="btn btn-primary">Check API Health</button>
                        <div id="healthResult" class="mt-3"></div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Excel File Upload Test</h5>
                    </div>
                    <div class="card-body">
                        <form id="uploadForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="excelFile" class="form-label">Select Excel File (.xlsx or .xls)</label>
                                <input type="file" class="form-control" id="excelFile" accept=".xlsx,.xls" required>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-upload me-1"></i>
                                Upload Excel File
                            </button>
                        </form>
                        <div id="uploadResult" class="mt-3"></div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Workbook Data</h5>
                    </div>
                    <div class="card-body">
                        <div id="workbookData">
                            <p class="text-muted">Upload an Excel file to see the data here.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentWorkbookId = null;

        // Health check
        document.getElementById('healthCheck').addEventListener('click', async () => {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Checking...';
            
            try {
                const response = await fetch('/api/test/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            API is healthy! Status: ${data.status}, Time: ${new Date(data.timestamp).toLocaleString()}
                        </div>
                    `;
                } else {
                    throw new Error('API returned error status');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        API health check failed: ${error.message}
                    </div>
                `;
            }
        });

        // File upload
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('excelFile');
            const resultDiv = document.getElementById('uploadResult');
            const workbookDiv = document.getElementById('workbookData');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="alert alert-warning">Please select a file.</div>';
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Uploading...';
            
            try {
                const response = await fetch('/api/excel/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentWorkbookId = data.workbookId;
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            ${data.message}
                        </div>
                    `;
                    
                    // Display workbook info
                    workbookDiv.innerHTML = `
                        <h6>Workbook ID: <code>${data.workbookId}</code></h6>
                        <h6>Worksheets:</h6>
                        <ul class="list-group">
                            ${data.worksheetNames.map(name => `
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    ${name}
                                    <button class="btn btn-sm btn-outline-primary" onclick="loadWorksheet('${name}')">
                                        <i class="fas fa-eye me-1"></i>Load
                                    </button>
                                </li>
                            `).join('')}
                        </ul>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Upload failed: ${data.message}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Upload error: ${error.message}
                    </div>
                `;
            }
        });

        // Load worksheet data
        async function loadWorksheet(worksheetName) {
            if (!currentWorkbookId) return;
            
            const workbookDiv = document.getElementById('workbookData');
            workbookDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Loading worksheet data...';
            
            try {
                const response = await fetch(`/api/excel/${currentWorkbookId}/worksheets/${encodeURIComponent(worksheetName)}`);
                const data = await response.json();
                
                if (response.ok) {
                    workbookDiv.innerHTML = `
                        <h6>Worksheet: ${data.name}</h6>
                        <p>Dimensions: ${data.maxRow} rows × ${data.maxColumn} columns</p>
                        <p>Images: ${data.images ? data.images.length : 0}</p>
                        <h6>Sample Cells (first 5 rows, 5 columns):</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                ${generateTableFromCells(data.cells, Math.min(5, data.maxRow), Math.min(5, data.maxColumn))}
                            </table>
                        </div>
                    `;
                } else {
                    workbookDiv.innerHTML = `
                        <div class="alert alert-danger">
                            Failed to load worksheet: ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                workbookDiv.innerHTML = `
                    <div class="alert alert-danger">
                        Error loading worksheet: ${error.message}
                    </div>
                `;
            }
        }

        function generateTableFromCells(cells, maxRows, maxCols) {
            if (!cells || cells.length === 0) return '<p>No cell data available</p>';
            
            let html = '<thead><tr><th></th>';
            for (let col = 1; col <= maxCols; col++) {
                html += `<th>${String.fromCharCode(64 + col)}</th>`;
            }
            html += '</tr></thead><tbody>';
            
            for (let row = 1; row <= maxRows; row++) {
                html += `<tr><th>${row}</th>`;
                for (let col = 1; col <= maxCols; col++) {
                    const cell = cells[row] && cells[row][col];
                    const value = cell ? (cell.displayValue || '') : '';
                    const title = cell && cell.formula ? `Formula: ${cell.formula}` : '';
                    html += `<td title="${title}">${value}</td>`;
                }
                html += '</tr>';
            }
            html += '</tbody>';
            
            return html;
        }

        // Auto-run health check on page load
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('healthCheck').click();
        });
    </script>
</body>
</html>
