{"name": "ExcelPreviewApp", "version": "0.1.0", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "ag-grid-react": "^31.0.0", "ag-grid-community": "^31.0.0", "ag-grid-enterprise": "^31.0.0", "bootstrap": "^5.3.0", "react-bootstrap": "^2.8.0", "react-router-dom": "^6.8.0", "axios": "^1.4.0", "react-dropzone": "^14.2.3", "react-tabs": "^6.0.1", "react-router-bootstrap": "^0.26.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}