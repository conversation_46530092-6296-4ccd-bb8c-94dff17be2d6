using Microsoft.AspNetCore.SpaServices.ReactDevelopmentServer;
using ExcelPreviewApp.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Register Excel service
builder.Services.AddSingleton<IExcelService, ExcelService>();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "https://localhost:3000",
                          "http://localhost:5000", "https://localhost:5001")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// In production, the React files will be served from this directory
// Temporarily disabled to allow API testing without npm
// builder.Services.AddSpaStaticFiles(configuration =>
// {
//     configuration.RootPath = "ClientApp/build";
// });

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
// Temporarily disabled to allow API testing without npm
// app.UseSpaStaticFiles();

app.UseCors("AllowReactApp");
app.UseRouting();
app.UseAuthorization();

app.MapControllers();

// Temporarily disabled to allow API testing without npm
// app.UseSpa(spa =>
// {
//     spa.Options.SourcePath = "ClientApp";
//
//     if (app.Environment.IsDevelopment())
//     {
//         spa.UseReactDevelopmentServer(npmScript: "start");
//     }
// });

app.Run();
