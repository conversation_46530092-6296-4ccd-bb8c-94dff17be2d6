.App {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.excel-upload-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.upload-dropzone {
  border: 2px dashed #007bff;
  border-radius: 8px;
  padding: 3rem;
  text-align: center;
  background-color: #f8f9ff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-dropzone:hover {
  border-color: #0056b3;
  background-color: #e6f3ff;
}

.upload-dropzone.active {
  border-color: #28a745;
  background-color: #e6ffe6;
}

.excel-preview-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.worksheet-tabs {
  background-color: white;
  border-bottom: 1px solid #dee2e6;
  padding: 0.5rem 1rem;
}

.worksheet-tab {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin-right: 0.25rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.worksheet-tab:hover {
  background-color: #e9ecef;
}

.worksheet-tab.active {
  background-color: white;
  border-bottom: 1px solid white;
  margin-bottom: -1px;
}

.excel-grid-container {
  flex: 1;
  background-color: white;
  border: 1px solid #dee2e6;
  border-top: none;
}

.ag-theme-alpine {
  --ag-header-height: 32px;
  --ag-row-height: 25px;
  --ag-font-size: 12px;
  --ag-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.ag-header-cell {
  background-color: #f8f9fa !important;
  border-right: 1px solid #dee2e6 !important;
  font-weight: 600;
}

.ag-cell {
  border-right: 1px solid #e9ecef !important;
  border-bottom: 1px solid #e9ecef !important;
}

.ag-cell.cell-bold {
  font-weight: bold;
}

.ag-cell.cell-italic {
  font-style: italic;
}

.ag-cell.cell-underline {
  text-decoration: underline;
}

.formula-bar {
  background-color: white;
  border-bottom: 1px solid #dee2e6;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.formula-input {
  flex: 1;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  font-family: 'Courier New', monospace;
}

.cell-address {
  font-weight: 600;
  min-width: 80px;
  padding: 0.375rem 0.75rem;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
}

.success-message {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
}

.excel-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.floating-image {
  position: absolute;
  z-index: 10;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar {
  background-color: white;
  border-bottom: 1px solid #dee2e6;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.toolbar-button {
  padding: 0.375rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.toolbar-button:hover {
  background-color: #f8f9fa;
}

.toolbar-button:active {
  background-color: #e9ecef;
}

@media (max-width: 768px) {
  .excel-upload-container {
    padding: 1rem;
  }
  
  .upload-dropzone {
    padding: 2rem 1rem;
  }
  
  .formula-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .cell-address {
    min-width: auto;
  }
}

/* Formula cell styling */
.formula-cell {
  position: relative;
  background-color: #e8f4fd !important;
  border-left: 3px solid #007bff !important;
}

.formula-icon {
  color: #007bff;
  font-size: 10px;
  margin-right: 4px;
  opacity: 0.7;
}

.formula-cell:hover {
  background-color: #d1ecf1 !important;
}

.formula-cell:hover .formula-icon {
  opacity: 1;
}

/* Enhanced AG Grid cell styling for formulas */
.ag-cell .formula-cell {
  display: inline-block;
  width: 100%;
  height: 100%;
  padding: 4px 8px;
  box-sizing: border-box;
}

/* Formula tooltip styling */
.ag-cell [title]:hover {
  position: relative;
}

.formula-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  font-family: 'Courier New', monospace;
  max-width: 300px;
  word-wrap: break-word;
  margin-bottom: 5px;
}

.formula-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #333;
}

/* Formula help popup */
.formula-help-popup {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #007bff;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  z-index: 1000;
  font-size: 13px;
  margin-top: 2px;
}

.formula-help-popup strong {
  color: #007bff;
  font-size: 14px;
}

.formula-help-popup em {
  color: #6c757d;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* Formula input editing state */
.formula-input.editing {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Formula and data type indicators */
.formula-indicator {
  cursor: help;
}

.data-type-indicator {
  font-size: 10px;
  cursor: help;
}
