using OfficeOpenXml;
using OfficeOpenXml.Drawing;
using ExcelPreviewApp.Models;
using System.Drawing;
using System.Text.RegularExpressions;
using EPPlusWorkbook = OfficeOpenXml.ExcelWorkbook;
using EPPlusWorksheet = OfficeOpenXml.ExcelWorksheet;
using EPPlusImage = OfficeOpenXml.Drawing.ExcelImage;
using ModelWorkbook = ExcelPreviewApp.Models.ExcelWorkbook;
using ModelWorksheet = ExcelPreviewApp.Models.ExcelWorksheet;
using ModelImage = ExcelPreviewApp.Models.ExcelImage;

namespace ExcelPreviewApp.Services;

public interface IExcelService
{
    Task<ModelWorkbook> ParseExcelFileAsync(Stream fileStream, string fileName);
    Task<ModelWorksheet> GetWorksheetAsync(string workbookId, string worksheetName);
    Task<ExcelCell> UpdateCellAsync(string workbookId, string worksheetName, string cellAddress, string value);
    Task<byte[]> GetImageAsync(string workbookId, string imageId);
    ModelWorkbook? GetWorkbook(string workbookId);
}

public class ExcelService : IExcelService
{
    private readonly Dictionary<string, ModelWorkbook> _workbooks = new();
    private readonly Dictionary<string, byte[]> _originalFiles = new();

    public ExcelService()
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public async Task<ModelWorkbook> ParseExcelFileAsync(Stream fileStream, string fileName)
    {
        var workbookId = Guid.NewGuid().ToString();
        var workbook = new ModelWorkbook
        {
            Id = workbookId,
            FileName = fileName,
            UploadedAt = DateTime.UtcNow
        };

        // Store original file for potential re-processing
        using var memoryStream = new MemoryStream();
        await fileStream.CopyToAsync(memoryStream);
        _originalFiles[workbookId] = memoryStream.ToArray();

        // Reset stream position for processing
        fileStream.Position = 0;

        using var package = new ExcelPackage(fileStream);
        
        foreach (var worksheet in package.Workbook.Worksheets)
        {
            var excelWorksheet = await ParseWorksheetAsync(worksheet);
            workbook.Worksheets.Add(excelWorksheet);
        }

        _workbooks[workbookId] = workbook;
        return workbook;
    }

    private Task<ModelWorksheet> ParseWorksheetAsync(EPPlusWorksheet worksheet)
    {
        var excelWorksheet = new ModelWorksheet
        {
            Name = worksheet.Name,
            Index = worksheet.Index
        };

        var dimension = worksheet.Dimension;
        if (dimension == null)
        {
            return Task.FromResult(excelWorksheet);
        }

        excelWorksheet.MaxRow = dimension.End.Row;
        excelWorksheet.MaxColumn = dimension.End.Column;

        // Initialize cells grid
        for (int row = 0; row <= dimension.End.Row; row++)
        {
            var cellRow = new List<ExcelCell>();
            for (int col = 0; col <= dimension.End.Column; col++)
            {
                cellRow.Add(new ExcelCell { Row = row, Column = col });
            }
            excelWorksheet.Cells.Add(cellRow);
        }

        // Parse cells
        for (int row = dimension.Start.Row; row <= dimension.End.Row; row++)
        {
            for (int col = dimension.Start.Column; col <= dimension.End.Column; col++)
            {
                var cell = worksheet.Cells[row, col];
                var excelCell = ParseCell(cell, row, col);
                
                if (row < excelWorksheet.Cells.Count && col < excelWorksheet.Cells[row].Count)
                {
                    excelWorksheet.Cells[row][col] = excelCell;
                }
            }
        }

        // Parse images
        foreach (var drawing in worksheet.Drawings)
        {
            if (drawing is ExcelPicture picture)
            {
                var excelImage = ParseImage(picture);
                excelWorksheet.Images.Add(excelImage);
            }
        }

        return Task.FromResult(excelWorksheet);
    }

    private ExcelCell ParseCell(ExcelRange cell, int row, int col)
    {
        var excelCell = new ExcelCell
        {
            Row = row,
            Column = col,
            Address = cell.Address,
            Value = cell.Value,
            Formula = cell.Formula,
            DisplayValue = cell.Text ?? string.Empty
        };

        // Parse formatting
        var style = cell.Style;
        excelCell.Format = new ExcelCellFormat
        {
            IsBold = style.Font.Bold,
            IsItalic = style.Font.Italic,
            IsUnderline = style.Font.UnderLine,
            FontFamily = style.Font.Name,
            FontSize = (int)style.Font.Size,
            HorizontalAlignment = style.HorizontalAlignment.ToString().ToLower(),
            VerticalAlignment = style.VerticalAlignment.ToString().ToLower(),
            NumberFormat = style.Numberformat.Format
        };

        // Parse colors
        if (style.Font.Color.Rgb != null)
        {
            excelCell.Format.FontColor = $"#{style.Font.Color.Rgb}";
        }

        if (style.Fill.BackgroundColor.Rgb != null)
        {
            excelCell.Format.BackgroundColor = $"#{style.Fill.BackgroundColor.Rgb}";
        }

        // Parse comment
        if (cell.Comment != null)
        {
            excelCell.Comment = cell.Comment.Text;
        }

        return excelCell;
    }

    private ModelImage ParseImage(ExcelPicture picture)
    {
        var imageId = Guid.NewGuid().ToString();

        return new ModelImage
        {
            Id = imageId,
            Name = picture.Name,
            ContentType = GetContentType(picture.Image.Type ?? ePictureType.Png),
            Data = picture.Image.ImageBytes,
            Left = picture.From.Column,
            Top = picture.From.Row,
            Width = picture.To.Column - picture.From.Column,
            Height = picture.To.Row - picture.From.Row,
            IsFloating = true // EPPlus pictures are typically floating
        };
    }

    private string GetContentType(ePictureType imageType)
    {
        return imageType switch
        {
            ePictureType.Jpg => "image/jpeg",
            ePictureType.Png => "image/png",
            ePictureType.Gif => "image/gif",
            ePictureType.Bmp => "image/bmp",
            _ => "image/png"
        };
    }

    public async Task<ModelWorksheet> GetWorksheetAsync(string workbookId, string worksheetName)
    {
        if (!_workbooks.TryGetValue(workbookId, out var workbook))
        {
            throw new ArgumentException("Workbook not found", nameof(workbookId));
        }

        var worksheet = workbook.Worksheets.FirstOrDefault(w => w.Name == worksheetName);
        if (worksheet == null)
        {
            throw new ArgumentException("Worksheet not found", nameof(worksheetName));
        }

        return await Task.FromResult(worksheet);
    }

    public async Task<ExcelCell> UpdateCellAsync(string workbookId, string worksheetName, string cellAddress, string value)
    {
        var worksheet = await GetWorksheetAsync(workbookId, worksheetName);
        
        // Parse cell address (e.g., "A1" -> row 1, col 1)
        var (row, col) = ParseCellAddress(cellAddress);
        
        if (row < worksheet.Cells.Count && col < worksheet.Cells[row].Count)
        {
            var cell = worksheet.Cells[row][col];
            cell.Value = value;
            cell.DisplayValue = value;
            
            // Clear formula if updating with a direct value
            if (!value.StartsWith("="))
            {
                cell.Formula = null;
            }
            else
            {
                cell.Formula = value;
            }
            
            return cell;
        }
        
        throw new ArgumentException("Invalid cell address", nameof(cellAddress));
    }

    public async Task<byte[]> GetImageAsync(string workbookId, string imageId)
    {
        if (!_workbooks.TryGetValue(workbookId, out var workbook))
        {
            throw new ArgumentException("Workbook not found", nameof(workbookId));
        }

        foreach (var worksheet in workbook.Worksheets)
        {
            var image = worksheet.Images.FirstOrDefault(i => i.Id == imageId);
            if (image != null)
            {
                return await Task.FromResult(image.Data);
            }
        }

        throw new ArgumentException("Image not found", nameof(imageId));
    }

    public ModelWorkbook? GetWorkbook(string workbookId)
    {
        _workbooks.TryGetValue(workbookId, out var workbook);
        return workbook;
    }

    private (int row, int col) ParseCellAddress(string address)
    {
        var match = Regex.Match(address, @"([A-Z]+)(\d+)");
        if (!match.Success)
        {
            throw new ArgumentException("Invalid cell address format", nameof(address));
        }

        var columnLetters = match.Groups[1].Value;
        var rowNumber = int.Parse(match.Groups[2].Value);

        var column = 0;
        for (int i = 0; i < columnLetters.Length; i++)
        {
            column = column * 26 + (columnLetters[i] - 'A' + 1);
        }

        return (rowNumber, column);
    }
}
