import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import axios from 'axios';
import ExcelGrid from './ExcelGrid';
import WorksheetTabs from './WorksheetTabs';
import FormulaBar from './FormulaBar';

function ExcelPreview() {
  const { workbookId } = useParams();
  const navigate = useNavigate();
  
  const [worksheets, setWorksheets] = useState([]);
  const [currentWorksheet, setCurrentWorksheet] = useState(null);
  const [worksheetData, setWorksheetData] = useState(null);
  const [selectedCell, setSelectedCell] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load worksheets list
  useEffect(() => {
    const loadWorksheets = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/excel/${workbookId}/worksheets`);
        setWorksheets(response.data);
        
        if (response.data.length > 0) {
          setCurrentWorksheet(response.data[0]);
        }
      } catch (err) {
        console.error('Error loading worksheets:', err);
        setError('Failed to load worksheets. The file may have expired or been removed.');
      } finally {
        setLoading(false);
      }
    };

    if (workbookId) {
      loadWorksheets();
    }
  }, [workbookId]);

  // Load worksheet data when current worksheet changes
  useEffect(() => {
    const loadWorksheetData = async () => {
      if (!currentWorksheet) return;

      try {
        setLoading(true);
        const response = await axios.get(
          `/api/excel/${workbookId}/worksheets/${encodeURIComponent(currentWorksheet)}`
        );
        setWorksheetData(response.data);
        setSelectedCell(null); // Clear selection when switching sheets
      } catch (err) {
        console.error('Error loading worksheet data:', err);
        setError(`Failed to load worksheet "${currentWorksheet}".`);
      } finally {
        setLoading(false);
      }
    };

    loadWorksheetData();
  }, [workbookId, currentWorksheet]);

  const handleWorksheetChange = useCallback((worksheetName) => {
    setCurrentWorksheet(worksheetName);
  }, []);

  const handleCellSelect = useCallback((cell) => {
    setSelectedCell(cell);
  }, []);

  const handleCellUpdate = useCallback(async (cellAddress, newValue) => {
    try {
      const response = await axios.put(
        `/api/excel/${workbookId}/worksheets/${encodeURIComponent(currentWorksheet)}/cells`,
        {
          workbookId,
          worksheetName: currentWorksheet,
          cellAddress,
          value: newValue
        }
      );

      if (response.data.success) {
        // Update the local data
        const updatedCell = response.data.updatedCell;
        setWorksheetData(prevData => {
          const newData = { ...prevData };
          const row = updatedCell.row;
          const col = updatedCell.column;
          
          if (newData.cells[row] && newData.cells[row][col]) {
            newData.cells[row][col] = updatedCell;
          }
          
          return newData;
        });

        // Update selected cell if it's the one we just updated
        if (selectedCell && selectedCell.address === cellAddress) {
          setSelectedCell(updatedCell);
        }
      } else {
        setError(response.data.message || 'Failed to update cell');
      }
    } catch (err) {
      console.error('Error updating cell:', err);
      setError('Failed to update cell. Please try again.');
    }
  }, [workbookId, currentWorksheet, selectedCell]);

  const handleFormulaUpdate = useCallback((newFormula) => {
    if (selectedCell) {
      handleCellUpdate(selectedCell.address, newFormula);
    }
  }, [selectedCell, handleCellUpdate]);

  const handleBackToUpload = () => {
    navigate('/');
  };

  if (loading && !worksheetData) {
    return (
      <div className="loading-spinner">
        <Spinner animation="border" variant="primary" />
        <span className="ms-2">Loading Excel file...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mt-4">
        <Alert variant="danger">
          <Alert.Heading>Error</Alert.Heading>
          <p>{error}</p>
          <hr />
          <div className="d-flex justify-content-end">
            <Button variant="outline-danger" onClick={handleBackToUpload}>
              Back to Upload
            </Button>
          </div>
        </Alert>
      </div>
    );
  }

  if (!worksheetData) {
    return (
      <div className="container mt-4">
        <Alert variant="warning">
          <Alert.Heading>No Data</Alert.Heading>
          <p>No worksheet data available.</p>
          <hr />
          <div className="d-flex justify-content-end">
            <Button variant="outline-warning" onClick={handleBackToUpload}>
              Back to Upload
            </Button>
          </div>
        </Alert>
      </div>
    );
  }

  return (
    <div className="excel-preview-container">
      <div className="toolbar">
        <Button variant="outline-secondary" size="sm" onClick={handleBackToUpload}>
          <i className="fas fa-arrow-left me-1"></i>
          Back to Upload
        </Button>
        <span className="text-muted">|</span>
        <span className="fw-bold">{currentWorksheet}</span>
        <span className="text-muted">
          ({worksheetData.maxRow} rows × {worksheetData.maxColumn} columns)
        </span>
      </div>

      <FormulaBar 
        selectedCell={selectedCell}
        onFormulaUpdate={handleFormulaUpdate}
      />

      <WorksheetTabs
        worksheets={worksheets}
        currentWorksheet={currentWorksheet}
        onWorksheetChange={handleWorksheetChange}
      />

      <div className="excel-grid-container">
        <ExcelGrid
          worksheetData={worksheetData}
          selectedCell={selectedCell}
          onCellSelect={handleCellSelect}
          onCellUpdate={handleCellUpdate}
          loading={loading}
        />
      </div>
    </div>
  );
}

export default ExcelPreview;
