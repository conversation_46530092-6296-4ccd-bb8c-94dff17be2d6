import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, Alert, Spinner, ListGroup } from 'react-bootstrap';
import axios from 'axios';

function ExcelUpload({ onWorkbookUploaded, currentWorkbook }) {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const navigate = useNavigate();

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setUploading(true);
    setError(null);
    setSuccess(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post('/api/excel/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setSuccess(`File "${file.name}" uploaded successfully!`);
        onWorkbookUploaded(response.data);
        
        // Navigate to preview after a short delay
        setTimeout(() => {
          navigate(`/preview/${response.data.workbookId}`);
        }, 1500);
      } else {
        setError(response.data.message || 'Upload failed');
      }
    } catch (err) {
      console.error('Upload error:', err);
      setError(
        err.response?.data?.message || 
        'Failed to upload file. Please try again.'
      );
    } finally {
      setUploading(false);
    }
  }, [onWorkbookUploaded, navigate]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    multiple: false,
    disabled: uploading
  });

  const handlePreviewExisting = () => {
    if (currentWorkbook?.workbookId) {
      navigate(`/preview/${currentWorkbook.workbookId}`);
    }
  };

  return (
    <div className="excel-upload-container">
      <Card>
        <Card.Header>
          <h4 className="mb-0">
            <i className="fas fa-upload me-2"></i>
            Upload Excel File
          </h4>
        </Card.Header>
        <Card.Body>
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError(null)}>
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert variant="success" dismissible onClose={() => setSuccess(null)}>
              <i className="fas fa-check-circle me-2"></i>
              {success}
            </Alert>
          )}

          <div
            {...getRootProps()}
            className={`upload-dropzone ${isDragActive ? 'active' : ''}`}
          >
            <input {...getInputProps()} />
            
            {uploading ? (
              <div className="text-center">
                <Spinner animation="border" variant="primary" className="mb-3" />
                <p className="mb-0">Uploading and processing your Excel file...</p>
              </div>
            ) : (
              <div>
                <i className="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                <h5>Drop your Excel file here</h5>
                <p className="text-muted mb-3">
                  or <strong>click to browse</strong>
                </p>
                <p className="small text-muted">
                  Supported formats: .xlsx, .xls (Max size: 10MB)
                </p>
              </div>
            )}
          </div>

          {currentWorkbook && (
            <div className="mt-4">
              <h6>Recently Uploaded:</h6>
              <Card className="mt-2">
                <Card.Body className="py-2">
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <strong>{currentWorkbook.fileName || 'Excel File'}</strong>
                      <div className="small text-muted">
                        Worksheets: {currentWorkbook.worksheetNames?.join(', ')}
                      </div>
                    </div>
                    <Button 
                      variant="outline-primary" 
                      size="sm"
                      onClick={handlePreviewExisting}
                    >
                      <i className="fas fa-eye me-1"></i>
                      Preview
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </div>
          )}
        </Card.Body>
      </Card>

      <Card className="mt-4">
        <Card.Header>
          <h5 className="mb-0">
            <i className="fas fa-info-circle me-2"></i>
            Features
          </h5>
        </Card.Header>
        <Card.Body>
          <ListGroup variant="flush">
            <ListGroup.Item className="px-0">
              <i className="fas fa-check text-success me-2"></i>
              View multiple worksheets with tab navigation
            </ListGroup.Item>
            <ListGroup.Item className="px-0">
              <i className="fas fa-check text-success me-2"></i>
              Excel-like grid interface with cell formatting
            </ListGroup.Item>
            <ListGroup.Item className="px-0">
              <i className="fas fa-check text-success me-2"></i>
              Formula display and inline editing
            </ListGroup.Item>
            <ListGroup.Item className="px-0">
              <i className="fas fa-check text-success me-2"></i>
              Image support (embedded and floating)
            </ListGroup.Item>
            <ListGroup.Item className="px-0">
              <i className="fas fa-check text-success me-2"></i>
              Cell comments and formatting preservation
            </ListGroup.Item>
          </ListGroup>
        </Card.Body>
      </Card>
    </div>
  );
}

export default ExcelUpload;
