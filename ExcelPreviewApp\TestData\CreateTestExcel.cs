using OfficeOpenXml;
using System.Drawing;

namespace ExcelPreviewApp.TestData;

public static class CreateTestExcel
{
    public static void CreateSampleExcelFile(string filePath)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        
        using var package = new ExcelPackage();
        
        // Create first worksheet
        var worksheet1 = package.Workbook.Worksheets.Add("Sales Data");
        
        // Add headers
        worksheet1.Cells[1, 1].Value = "Product";
        worksheet1.Cells[1, 2].Value = "Quantity";
        worksheet1.Cells[1, 3].Value = "Price";
        worksheet1.Cells[1, 4].Value = "Total";
        worksheet1.Cells[1, 5].Value = "Date";
        
        // Format headers
        using (var range = worksheet1.Cells[1, 1, 1, 5])
        {
            range.Style.Font.Bold = true;
            range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            range.Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
        }
        
        // Add sample data
        var products = new[]
        {
            new { Product = "Laptop", Quantity = 10, Price = 999.99, Date = DateTime.Now.AddDays(-30) },
            new { Product = "Mouse", Quantity = 25, Price = 29.99, Date = DateTime.Now.AddDays(-25) },
            new { Product = "Keyboard", Quantity = 15, Price = 79.99, Date = DateTime.Now.AddDays(-20) },
            new { Product = "Monitor", Quantity = 8, Price = 299.99, Date = DateTime.Now.AddDays(-15) },
            new { Product = "Headphones", Quantity = 20, Price = 149.99, Date = DateTime.Now.AddDays(-10) }
        };
        
        for (int i = 0; i < products.Length; i++)
        {
            var row = i + 2;
            worksheet1.Cells[row, 1].Value = products[i].Product;
            worksheet1.Cells[row, 2].Value = products[i].Quantity;
            worksheet1.Cells[row, 3].Value = products[i].Price;
            worksheet1.Cells[row, 4].Formula = $"B{row}*C{row}"; // Formula for total
            worksheet1.Cells[row, 5].Value = products[i].Date;
            worksheet1.Cells[row, 5].Style.Numberformat.Format = "mm/dd/yyyy";
        }
        
        // Add summary row
        var summaryRow = products.Length + 3;
        worksheet1.Cells[summaryRow, 1].Value = "TOTAL:";
        worksheet1.Cells[summaryRow, 1].Style.Font.Bold = true;
        worksheet1.Cells[summaryRow, 4].Formula = $"SUM(D2:D{products.Length + 1})";
        worksheet1.Cells[summaryRow, 4].Style.Font.Bold = true;
        
        // Auto-fit columns
        worksheet1.Cells.AutoFitColumns();
        
        // Create second worksheet
        var worksheet2 = package.Workbook.Worksheets.Add("Formulas Demo");
        
        // Add various formula examples
        worksheet2.Cells[1, 1].Value = "Formula Examples";
        worksheet2.Cells[1, 1].Style.Font.Bold = true;
        worksheet2.Cells[1, 1].Style.Font.Size = 14;
        
        worksheet2.Cells[3, 1].Value = "Basic Math:";
        worksheet2.Cells[3, 2].Formula = "5+3*2";
        
        worksheet2.Cells[4, 1].Value = "Date Function:";
        worksheet2.Cells[4, 2].Formula = "TODAY()";
        
        worksheet2.Cells[5, 1].Value = "Text Function:";
        worksheet2.Cells[5, 2].Formula = "CONCATENATE(\"Hello\", \" \", \"World\")";
        
        worksheet2.Cells[6, 1].Value = "Conditional:";
        worksheet2.Cells[6, 2].Formula = "IF(10>5,\"True\",\"False\")";
        
        worksheet2.Cells[7, 1].Value = "Random Number:";
        worksheet2.Cells[7, 2].Formula = "RAND()*100";
        
        // Auto-fit columns
        worksheet2.Cells.AutoFitColumns();
        
        // Create third worksheet with more complex data
        var worksheet3 = package.Workbook.Worksheets.Add("Employee Data");
        
        // Headers
        var headers = new[] { "ID", "Name", "Department", "Salary", "Bonus", "Total Compensation", "Performance" };
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet3.Cells[1, i + 1].Value = headers[i];
            worksheet3.Cells[1, i + 1].Style.Font.Bold = true;
        }
        
        // Sample employee data
        var employees = new[]
        {
            new { ID = 1, Name = "John Doe", Department = "IT", Salary = 75000, Bonus = 5000 },
            new { ID = 2, Name = "Jane Smith", Department = "HR", Salary = 65000, Bonus = 3000 },
            new { ID = 3, Name = "Bob Johnson", Department = "Finance", Salary = 80000, Bonus = 8000 },
            new { ID = 4, Name = "Alice Brown", Department = "IT", Salary = 70000, Bonus = 4000 },
            new { ID = 5, Name = "Charlie Wilson", Department = "Marketing", Salary = 60000, Bonus = 2000 }
        };
        
        for (int i = 0; i < employees.Length; i++)
        {
            var row = i + 2;
            worksheet3.Cells[row, 1].Value = employees[i].ID;
            worksheet3.Cells[row, 2].Value = employees[i].Name;
            worksheet3.Cells[row, 3].Value = employees[i].Department;
            worksheet3.Cells[row, 4].Value = employees[i].Salary;
            worksheet3.Cells[row, 5].Value = employees[i].Bonus;
            worksheet3.Cells[row, 6].Formula = $"D{row}+E{row}"; // Total compensation
            worksheet3.Cells[row, 7].Formula = $"IF(F{row}>75000,\"Excellent\",IF(F{row}>65000,\"Good\",\"Average\"))";
        }
        
        // Format salary columns as currency
        worksheet3.Cells[2, 4, employees.Length + 1, 6].Style.Numberformat.Format = "$#,##0";
        
        worksheet3.Cells.AutoFitColumns();
        
        // Save the file
        var fileInfo = new FileInfo(filePath);
        package.SaveAs(fileInfo);
    }
}
