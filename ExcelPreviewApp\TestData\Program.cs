using ExcelPreviewApp.TestData;

Console.WriteLine("Creating test Excel file...");

var filePath = Path.Combine(Directory.GetCurrentDirectory(), "SampleData.xlsx");
CreateTestExcel.CreateSampleExcelFile(filePath);

Console.WriteLine($"Test Excel file created at: {filePath}");
Console.WriteLine("File contains:");
Console.WriteLine("- Sales Data worksheet with products, quantities, prices, and formulas");
Console.WriteLine("- Formulas Demo worksheet with various Excel formula examples");
Console.WriteLine("- Employee Data worksheet with conditional formulas and formatting");
Console.WriteLine();
Console.WriteLine("You can now upload this file to test the Excel Preview application.");
