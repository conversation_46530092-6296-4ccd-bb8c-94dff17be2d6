import React, { useMemo, useCallback, useRef, useEffect } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { Spinner } from 'react-bootstrap';

function ExcelGrid({ worksheetData, selectedCell, onCellSelect, onCellUpdate, loading }) {
  const gridRef = useRef();

  // Convert Excel column number to letter (1 = A, 2 = B, etc.)
  const numberToColumn = useCallback((num) => {
    let result = '';
    while (num > 0) {
      num--;
      result = String.fromCharCode(65 + (num % 26)) + result;
      num = Math.floor(num / 26);
    }
    return result;
  }, []);

  // Prepare column definitions
  const columnDefs = useMemo(() => {
    if (!worksheetData || !worksheetData.cells || worksheetData.cells.length === 0) {
      return [];
    }

    const columns = [];
    
    // Add row number column
    columns.push({
      headerName: '',
      field: 'rowNumber',
      width: 50,
      pinned: 'left',
      cellStyle: { 
        backgroundColor: '#f8f9fa', 
        fontWeight: 'bold',
        textAlign: 'center',
        border: '1px solid #dee2e6'
      },
      suppressMenu: true,
      sortable: false,
      filter: false,
      resizable: false
    });

    // Add columns for each Excel column
    for (let col = 1; col <= worksheetData.maxColumn; col++) {
      const columnLetter = numberToColumn(col);
      columns.push({
        headerName: columnLetter,
        field: `col_${col}`,
        width: 100,
        editable: true,
        cellStyle: (params) => {
          const cell = params.data?.cells?.[col];
          if (!cell) return {};

          const style = {
            border: '1px solid #e9ecef'
          };

          if (cell.format) {
            if (cell.format.isBold) style.fontWeight = 'bold';
            if (cell.format.isItalic) style.fontStyle = 'italic';
            if (cell.format.isUnderline) style.textDecoration = 'underline';
            if (cell.format.fontColor) style.color = cell.format.fontColor;
            if (cell.format.backgroundColor) style.backgroundColor = cell.format.backgroundColor;
            if (cell.format.fontSize) style.fontSize = `${cell.format.fontSize}px`;
            if (cell.format.fontFamily) style.fontFamily = cell.format.fontFamily;
            
            // Text alignment
            if (cell.format.horizontalAlignment) {
              style.textAlign = cell.format.horizontalAlignment;
            }
          }

          return style;
        },
        valueGetter: (params) => {
          const cell = params.data?.cells?.[col];
          return cell?.displayValue || '';
        },
        valueSetter: (params) => {
          if (params.data && params.data.cells && params.data.cells[col]) {
            const cellAddress = `${columnLetter}${params.data.rowNumber}`;
            onCellUpdate(cellAddress, params.newValue);
            return true;
          }
          return false;
        },
        cellRenderer: (params) => {
          const cell = params.data?.cells?.[col];
          if (!cell) return '';

          // Show formula in tooltip if available
          const title = cell.formula ? `Formula: ${cell.formula}` : '';
          const hasFormula = cell.formula && cell.formula.trim() !== '';

          // Add visual indicator for formula cells
          const className = hasFormula ? 'formula-cell' : '';
          const formulaIcon = hasFormula ? '<i class="fas fa-function formula-icon"></i>' : '';

          return `<span class="${className}" title="${title}">${formulaIcon}${params.value || ''}</span>`;
        },
        onCellClicked: (params) => {
          const cell = params.data?.cells?.[col];
          if (cell && onCellSelect) {
            const cellAddress = `${columnLetter}${params.data.rowNumber}`;
            onCellSelect({
              address: cellAddress,
              value: cell.value || '',
              displayValue: cell.displayValue || '',
              formula: cell.formula || '',
              dataType: cell.dataType || 'String'
            });
          }
        },
        onCellDoubleClicked: (params) => {
          const cell = params.data?.cells?.[col];
          if (cell && cell.formula) {
            // Show formula in edit mode
            const cellAddress = `${columnLetter}${params.data.rowNumber}`;
            // You can implement formula editing dialog here
            console.log(`Edit formula for ${cellAddress}: ${cell.formula}`);
          }
        }
      });
    }

    return columns;
  }, [worksheetData, numberToColumn, onCellUpdate]);

  // Prepare row data
  const rowData = useMemo(() => {
    if (!worksheetData || !worksheetData.cells) {
      return [];
    }

    const rows = [];
    for (let row = 1; row <= worksheetData.maxRow; row++) {
      const rowObj = {
        rowNumber: row,
        cells: {}
      };

      // Add cell data for each column
      for (let col = 1; col <= worksheetData.maxColumn; col++) {
        if (worksheetData.cells[row] && worksheetData.cells[row][col]) {
          rowObj.cells[col] = worksheetData.cells[row][col];
        }
      }

      rows.push(rowObj);
    }

    return rows;
  }, [worksheetData]);

  // Handle cell selection
  const onCellClicked = useCallback((event) => {
    const { colDef, data } = event;
    
    if (colDef.field !== 'rowNumber' && colDef.field.startsWith('col_')) {
      const colNum = parseInt(colDef.field.replace('col_', ''));
      const cell = data?.cells?.[colNum];
      
      if (cell) {
        onCellSelect(cell);
      }
    }
  }, [onCellSelect]);

  // Grid options
  const gridOptions = useMemo(() => ({
    suppressRowClickSelection: true,
    suppressCellSelection: false,
    enableCellTextSelection: true,
    suppressMenuHide: true,
    suppressColumnMoveAnimation: true,
    animateRows: false,
    enableRangeSelection: true,
    suppressMultiRangeSelection: false,
    enableFillHandle: true,
    undoRedoCellEditing: true,
    undoRedoCellEditingLimit: 20,
    stopEditingWhenCellsLoseFocus: true,
    enterMovesDown: true,
    enterMovesDownAfterEdit: true,
    suppressHorizontalScroll: false,
    suppressVerticalScroll: false,
    alwaysShowHorizontalScroll: true,
    alwaysShowVerticalScroll: true
  }), []);

  // Auto-size columns on data change
  useEffect(() => {
    if (gridRef.current && worksheetData) {
      const api = gridRef.current.api;
      setTimeout(() => {
        api.sizeColumnsToFit();
      }, 100);
    }
  }, [worksheetData]);

  if (loading) {
    return (
      <div className="loading-spinner">
        <Spinner animation="border" variant="primary" />
        <span className="ms-2">Loading worksheet data...</span>
      </div>
    );
  }

  if (!worksheetData || !worksheetData.cells) {
    return (
      <div className="d-flex justify-content-center align-items-center h-100">
        <div className="text-muted">
          <i className="fas fa-table fa-3x mb-3"></i>
          <p>No data to display</p>
        </div>
      </div>
    );
  }

  return (
    <div className="ag-theme-alpine" style={{ height: '100%', width: '100%' }}>
      <AgGridReact
        ref={gridRef}
        columnDefs={columnDefs}
        rowData={rowData}
        gridOptions={gridOptions}
        onCellClicked={onCellClicked}
        suppressLoadingOverlay={true}
        suppressNoRowsOverlay={true}
        headerHeight={32}
        rowHeight={25}
      />
    </div>
  );
}

export default ExcelGrid;
