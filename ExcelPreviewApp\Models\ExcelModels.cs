namespace ExcelPreviewApp.Models;

public class ExcelWorkbook
{
    public string Id { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public DateTime UploadedAt { get; set; }
    public List<ExcelWorksheet> Worksheets { get; set; } = new();
}

public class ExcelWorksheet
{
    public string Name { get; set; } = string.Empty;
    public int Index { get; set; }
    public List<List<ExcelCell>> Cells { get; set; } = new();
    public List<ExcelImage> Images { get; set; } = new();
    public int MaxRow { get; set; }
    public int MaxColumn { get; set; }
}

public class ExcelCell
{
    public int Row { get; set; }
    public int Column { get; set; }
    public string Address { get; set; } = string.Empty;
    public object? Value { get; set; }
    public string? Formula { get; set; }
    public string DisplayValue { get; set; } = string.Empty;
    public ExcelCellFormat Format { get; set; } = new();
    public string? Comment { get; set; }
}

public class ExcelCellFormat
{
    public bool IsBold { get; set; }
    public bool IsItalic { get; set; }
    public bool IsUnderline { get; set; }
    public string? FontColor { get; set; }
    public string? BackgroundColor { get; set; }
    public string? FontFamily { get; set; }
    public int FontSize { get; set; }
    public string HorizontalAlignment { get; set; } = "left";
    public string VerticalAlignment { get; set; } = "top";
    public string? NumberFormat { get; set; }
}

public class ExcelImage
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public int Left { get; set; }
    public int Top { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public bool IsFloating { get; set; }
    public string? CellAddress { get; set; }
}

public class UploadResponse
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public string? WorkbookId { get; set; }
    public List<string> WorksheetNames { get; set; } = new();
}

public class CellUpdateRequest
{
    public string WorkbookId { get; set; } = string.Empty;
    public string WorksheetName { get; set; } = string.Empty;
    public string CellAddress { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}

public class CellUpdateResponse
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public ExcelCell? UpdatedCell { get; set; }
    public List<ExcelCell>? AffectedCells { get; set; }
}
