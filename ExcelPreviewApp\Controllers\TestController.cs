using Microsoft.AspNetCore.Mvc;

namespace ExcelPreviewApp.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
    }

    [HttpGet("info")]
    public IActionResult Info()
    {
        return Ok(new 
        { 
            application = "Excel Preview App",
            version = "1.0.0",
            features = new[]
            {
                "Excel file upload (.xlsx, .xls)",
                "Multi-worksheet support",
                "Cell editing and formula display",
                "Image extraction and display",
                "Excel-like grid interface"
            }
        });
    }
}
