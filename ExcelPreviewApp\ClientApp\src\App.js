import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import './App.css';

import ExcelUpload from './components/ExcelUpload';
import ExcelPreview from './components/ExcelPreview';
import Navigation from './components/Navigation';

function App() {
  const [currentWorkbook, setCurrentWorkbook] = useState(null);

  return (
    <Router>
      <div className="App">
        <Navigation />
        <div className="container-fluid mt-3">
          <Routes>
            <Route
              path="/"
              element={
                <ExcelUpload
                  onWorkbookUploaded={setCurrentWorkbook}
                  currentWorkbook={currentWorkbook}
                />
              }
            />
            <Route
              path="/preview/:workbookId"
              element={<ExcelPreview />}
            />
          </Routes>
        </div>
      </div>
    </Router>
  );
}

export default App;
