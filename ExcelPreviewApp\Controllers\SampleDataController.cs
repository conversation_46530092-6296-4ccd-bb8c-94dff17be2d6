using Microsoft.AspNetCore.Mvc;
using ExcelPreviewApp.Models;
using ExcelPreviewApp.Services;

namespace ExcelPreviewApp.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ExcelController : ControllerBase
{
    private readonly IExcelService _excelService;
    private readonly ILogger<ExcelController> _logger;

    public ExcelController(IExcelService excelService, ILogger<ExcelController> logger)
    {
        _excelService = excelService;
        _logger = logger;
    }

    [HttpPost("upload")]
    public async Task<ActionResult<UploadResponse>> UploadExcel(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new UploadResponse
                {
                    Success = false,
                    Message = "No file uploaded"
                });
            }

            var allowedExtensions = new[] { ".xlsx", ".xls" };
            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();

            if (!allowedExtensions.Contains(fileExtension))
            {
                return BadRequest(new UploadResponse
                {
                    Success = false,
                    Message = "Only Excel files (.xlsx, .xls) are allowed"
                });
            }

            using var stream = file.OpenReadStream();
            var workbook = await _excelService.ParseExcelFileAsync(stream, file.FileName);

            return Ok(new UploadResponse
            {
                Success = true,
                Message = "File uploaded successfully",
                WorkbookId = workbook.Id,
                WorksheetNames = workbook.Worksheets.Select(w => w.Name).ToList()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading Excel file");
            return StatusCode(500, new UploadResponse
            {
                Success = false,
                Message = "Error processing Excel file: " + ex.Message
            });
        }
    }

    [HttpGet("{workbookId}/worksheets")]
    public ActionResult<List<string>> GetWorksheets(string workbookId)
    {
        try
        {
            var workbook = _excelService.GetWorkbook(workbookId);
            if (workbook == null)
            {
                return NotFound("Workbook not found");
            }

            return Ok(workbook.Worksheets.Select(w => w.Name).ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting worksheets for workbook {WorkbookId}", workbookId);
            return StatusCode(500, "Error retrieving worksheets");
        }
    }

    [HttpGet("{workbookId}/worksheets/{worksheetName}")]
    public async Task<ActionResult<ExcelWorksheet>> GetWorksheet(string workbookId, string worksheetName)
    {
        try
        {
            var worksheet = await _excelService.GetWorksheetAsync(workbookId, worksheetName);
            return Ok(worksheet);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting worksheet {WorksheetName} for workbook {WorkbookId}",
                worksheetName, workbookId);
            return StatusCode(500, "Error retrieving worksheet");
        }
    }

    [HttpPut("{workbookId}/worksheets/{worksheetName}/cells")]
    public async Task<ActionResult<CellUpdateResponse>> UpdateCell(
        string workbookId,
        string worksheetName,
        [FromBody] CellUpdateRequest request)
    {
        try
        {
            if (request.WorkbookId != workbookId || request.WorksheetName != worksheetName)
            {
                return BadRequest("Request parameters don't match URL parameters");
            }

            var updatedCell = await _excelService.UpdateCellAsync(
                workbookId, worksheetName, request.CellAddress, request.Value);

            return Ok(new CellUpdateResponse
            {
                Success = true,
                Message = "Cell updated successfully",
                UpdatedCell = updatedCell
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new CellUpdateResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating cell {CellAddress} in worksheet {WorksheetName} for workbook {WorkbookId}",
                request.CellAddress, worksheetName, workbookId);
            return StatusCode(500, new CellUpdateResponse
            {
                Success = false,
                Message = "Error updating cell"
            });
        }
    }

    [HttpGet("{workbookId}/images/{imageId}")]
    public async Task<IActionResult> GetImage(string workbookId, string imageId)
    {
        try
        {
            var imageData = await _excelService.GetImageAsync(workbookId, imageId);
            return File(imageData, "image/png"); // Default to PNG, could be enhanced to detect actual type
        }
        catch (ArgumentException)
        {
            return NotFound("Image not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting image {ImageId} for workbook {WorkbookId}", imageId, workbookId);
            return StatusCode(500, "Error retrieving image");
        }
    }
}
