import React, { useState, useEffect, useRef } from 'react';
import { Form, Button, OverlayTrigger, Tooltip } from 'react-bootstrap';

function FormulaBar({ selectedCell, onFormulaUpdate }) {
  const [formulaValue, setFormulaValue] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [showFormulaHelp, setShowFormulaHelp] = useState(false);
  const inputRef = useRef(null);

  useEffect(() => {
    if (selectedCell) {
      // Show formula if available, otherwise show the display value
      const value = selectedCell.formula || selectedCell.displayValue || '';
      setFormulaValue(value);
    } else {
      setFormulaValue('');
    }
    setIsEditing(false);
  }, [selectedCell]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setFormulaValue(value);

    // Show formula help if user starts typing a formula
    setShowFormulaHelp(value.startsWith('=') && value.length > 1);
  };

  const handleInputFocus = () => {
    setIsEditing(true);
    // If cell has a formula, show it for editing
    if (selectedCell?.formula) {
      setFormulaValue(selectedCell.formula);
    }
  };

  const handleInputBlur = () => {
    setIsEditing(false);
    setShowFormulaHelp(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (selectedCell && onFormulaUpdate) {
      onFormulaUpdate(formulaValue);
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSubmit(e);
    } else if (e.key === 'Escape') {
      // Reset to original value
      if (selectedCell) {
        const originalValue = selectedCell.formula || selectedCell.displayValue || '';
        setFormulaValue(originalValue);
      }
      setIsEditing(false);
      setShowFormulaHelp(false);
    } else if (e.key === 'F2') {
      // F2 to edit formula
      e.preventDefault();
      setIsEditing(true);
      inputRef.current?.focus();
    }
  };

  const getFormulaHelp = () => {
    if (!showFormulaHelp || !formulaValue.startsWith('=')) return null;

    const commonFunctions = [
      { name: 'SUM', description: 'Adds all numbers in a range', example: '=SUM(A1:A10)' },
      { name: 'AVERAGE', description: 'Calculates the average of numbers', example: '=AVERAGE(A1:A10)' },
      { name: 'COUNT', description: 'Counts cells with numbers', example: '=COUNT(A1:A10)' },
      { name: 'IF', description: 'Returns one value if true, another if false', example: '=IF(A1>10,"High","Low")' },
      { name: 'CONCATENATE', description: 'Joins text strings', example: '=CONCATENATE(A1," ",B1)' },
      { name: 'TODAY', description: 'Returns current date', example: '=TODAY()' },
      { name: 'NOW', description: 'Returns current date and time', example: '=NOW()' }
    ];

    const currentFunction = formulaValue.substring(1).toUpperCase().split('(')[0];
    const matchingFunction = commonFunctions.find(f => f.name.startsWith(currentFunction));

    if (matchingFunction) {
      return (
        <div className="formula-help-popup">
          <strong>{matchingFunction.name}</strong>: {matchingFunction.description}
          <br />
          <em>Example: {matchingFunction.example}</em>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="formula-bar">
      <div className="cell-address">
        {selectedCell ? selectedCell.address : 'A1'}
      </div>

      <div className="formula-input-container" style={{ flex: 1, position: 'relative' }}>
        <Form onSubmit={handleSubmit} className="d-flex">
          <Form.Control
            ref={inputRef}
            type="text"
            value={formulaValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            onKeyDown={handleKeyDown}
            placeholder={selectedCell ? "Enter value or formula (start with =)" : "Select a cell to edit"}
            disabled={!selectedCell}
            className={`formula-input ${isEditing ? 'editing' : ''}`}
          />

          {isEditing && (
            <Button
              type="submit"
              variant="outline-primary"
              size="sm"
              className="ms-2"
              disabled={!selectedCell}
            >
              <i className="fas fa-check"></i>
            </Button>
          )}
        </Form>

        {getFormulaHelp()}
      </div>

      <div className="formula-info d-flex align-items-center ms-2">
        {selectedCell?.formula && (
          <OverlayTrigger
            placement="top"
            overlay={<Tooltip>This cell contains a formula</Tooltip>}
          >
            <span className="formula-indicator me-2">
              <i className="fas fa-function text-primary"></i>
            </span>
          </OverlayTrigger>
        )}
        {selectedCell?.dataType && (
          <OverlayTrigger
            placement="top"
            overlay={<Tooltip>Data type: {selectedCell.dataType}</Tooltip>}
          >
            <span className="data-type-indicator badge bg-secondary">
              {selectedCell.dataType}
            </span>
          </OverlayTrigger>
        )}
      </div>
    </div>
  );
}

export default FormulaBar;
