import React, { useState, useEffect } from 'react';
import { Form, Button } from 'react-bootstrap';

function FormulaBar({ selectedCell, onFormulaUpdate }) {
  const [formulaValue, setFormulaValue] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (selectedCell) {
      // Show formula if available, otherwise show the display value
      const value = selectedCell.formula || selectedCell.displayValue || '';
      setFormulaValue(value);
    } else {
      setFormulaValue('');
    }
    setIsEditing(false);
  }, [selectedCell]);

  const handleInputChange = (e) => {
    setFormulaValue(e.target.value);
  };

  const handleInputFocus = () => {
    setIsEditing(true);
  };

  const handleInputBlur = () => {
    setIsEditing(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (selectedCell && onFormulaUpdate) {
      onFormulaUpdate(formulaValue);
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSubmit(e);
    } else if (e.key === 'Escape') {
      // Reset to original value
      if (selectedCell) {
        const originalValue = selectedCell.formula || selectedCell.displayValue || '';
        setFormulaValue(originalValue);
      }
      setIsEditing(false);
    }
  };

  return (
    <div className="formula-bar">
      <div className="cell-address">
        {selectedCell ? selectedCell.address : ''}
      </div>
      
      <Form onSubmit={handleSubmit} className="d-flex flex-grow-1">
        <Form.Control
          type="text"
          value={formulaValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          placeholder={selectedCell ? "Enter value or formula (start with =)" : "Select a cell to edit"}
          disabled={!selectedCell}
          className="formula-input"
        />
        
        {isEditing && (
          <Button 
            type="submit" 
            variant="outline-primary" 
            size="sm" 
            className="ms-2"
            disabled={!selectedCell}
          >
            <i className="fas fa-check"></i>
          </Button>
        )}
      </Form>
    </div>
  );
}

export default FormulaBar;
